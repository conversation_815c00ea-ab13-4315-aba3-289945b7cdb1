-- 为 employee_teacher_statistics 表添加新的抽佣相关字段
-- 执行时间: 2025-08-05
-- 描述: 添加4个新字段用于统计抽佣相关数据

USE `wendao101-order`;

-- 添加已提现金额收取的毛抽佣费+未提现金额将来要抽的毛抽佣费用
ALTER TABLE `employee_teacher_statistics` 
ADD COLUMN `total_withdrawn_fee` DECIMAL(10,2) DEFAULT 0.00 COMMENT '已提现金额收取的毛抽佣费+未提现金额将来要抽的毛抽佣费用';

-- 添加已提现净抽佣
ALTER TABLE `employee_teacher_statistics` 
ADD COLUMN `withdrawn_net_commission` DECIMAL(10,2) DEFAULT 0.00 COMMENT '已提现净抽佣';

-- 添加未提现净抽佣
ALTER TABLE `employee_teacher_statistics` 
ADD COLUMN `not_withdrawn_net_commission` DECIMAL(10,2) DEFAULT 0.00 COMMENT '未提现净抽佣';

-- 添加已提现净抽佣+未提现净抽佣
ALTER TABLE `employee_teacher_statistics` 
ADD COLUMN `total_withdrawn_net_commission` DECIMAL(10,2) DEFAULT 0.00 COMMENT '已提现净抽佣+未提现净抽佣';

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'wendao101-order' 
-- AND TABLE_NAME = 'employee_teacher_statistics' 
-- AND COLUMN_NAME IN ('total_withdrawn_fee', 'withdrawn_net_commission', 'not_withdrawn_net_commission', 'total_withdrawn_net_commission');
