<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.EmployeeTeacherStatisticsMapper">
    
    <resultMap type="EmployeeTeacherStatistics" id="EmployeeTeacherStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statisticsDate"    column="statistics_date"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="mobile"    column="mobile"    />
        <result property="shopName"    column="shop_name"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="dealAmount"    column="deal_amount"    />
        <result property="withdrawnAmount"    column="withdrawn_amount"    />
        <result property="moneyInTransit"    column="money_in_transit"    />
        <result property="withdrawableAmount"    column="withdrawable_amount"    />
        <result property="serviceFee"    column="service_fee"    />
        <result property="serviceFeeRate"    column="service_fee_rate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="timeQueryStr"    column="time_query_str"    />
        <result property="withdrawnAmountFee"    column="withdrawn_amount_fee"    />
        <result property="notWithdrawnFee"    column="not_withdrawn_fee"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderNickName"    column="leader_nick_name"    />
        <result property="parentLeaderId"    column="parent_leader_id"    />
        <result property="parentLeaderNickName"    column="parent_leader_nick_name"    />
        <result property="orderNum"    column="order_num"    />
        <result property="totalWithdrawnFee"    column="total_withdrawn_fee"    />
        <result property="withdrawnNetCommission"    column="withdrawn_net_commission"    />
        <result property="notWithdrawnNetCommission"    column="not_withdrawn_net_commission"    />
        <result property="totalWithdrawnNetCommission"    column="total_withdrawn_net_commission"    />
    </resultMap>

    <sql id="selectEmployeeTeacherStatisticsVo">
        SELECT
            id,
            statistics_date,
            teacher_id,
            mobile,
            shop_name,
            app_name_type,
            deal_amount,
            withdrawn_amount,
            money_in_transit,
            withdrawable_amount,
            service_fee,
            service_fee_rate,
            create_time,
            update_time,
            time_query_str,
            withdrawn_amount_fee,
            not_withdrawn_fee,
            employee_id,
            nick_name,
            leader_id,
            leader_nick_name,
            parent_leader_id,
            parent_leader_nick_name,
            order_num,
            total_withdrawn_fee,
            withdrawn_net_commission,
            not_withdrawn_net_commission,
            total_withdrawn_net_commission
        FROM
            `wendao101-order`.employee_teacher_statistics
    </sql>

    <select id="selectEmployeeTeacherStatisticsList" parameterType="EmployeeTeacherStatistics" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        <where>  
            <if test="statisticsDate != null "> and statistics_date = #{statisticsDate}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectEmployeeTeacherStatisticsById" parameterType="Long" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where id = #{id}
    </select>
    <select id="selectEmployeeTeacherStatisticsByTeacherId" resultMap="EmployeeTeacherStatisticsResult">
        <include refid="selectEmployeeTeacherStatisticsVo"/>
        where teacher_id = #{teacherId} limit 1
    </select>
    <select id="selectPromoterIdsByTeacherId" resultType="java.lang.Long">
        select id from `wendao101-teacher`.promoter where teacher_id = #{teacherId}
    </select>

    <insert id="insertEmployeeTeacherStatistics" parameterType="EmployeeTeacherStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.employee_teacher_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statisticsDate != null">statistics_date,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="mobile != null">mobile,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="dealAmount != null">deal_amount,</if>
            <if test="withdrawnAmount != null">withdrawn_amount,</if>
            <if test="moneyInTransit != null">money_in_transit,</if>
            <if test="withdrawableAmount != null">withdrawable_amount,</if>
            <if test="serviceFee != null">service_fee,</if>
            <if test="serviceFeeRate != null">service_fee_rate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="timeQueryStr != null">time_query_str,</if>
            <if test="withdrawnAmountFee != null">withdrawn_amount_fee,</if>
            <if test="notWithdrawnFee != null">not_withdrawn_fee,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="leaderId != null">leader_id,</if>
            <if test="leaderNickName != null">leader_nick_name,</if>
            <if test="parentLeaderId != null">parent_leader_id,</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="totalWithdrawnFee != null">total_withdrawn_fee,</if>
            <if test="withdrawnNetCommission != null">withdrawn_net_commission,</if>
            <if test="notWithdrawnNetCommission != null">not_withdrawn_net_commission,</if>
            <if test="totalWithdrawnNetCommission != null">total_withdrawn_net_commission,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statisticsDate != null">#{statisticsDate},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="dealAmount != null">#{dealAmount},</if>
            <if test="withdrawnAmount != null">#{withdrawnAmount},</if>
            <if test="moneyInTransit != null">#{moneyInTransit},</if>
            <if test="withdrawableAmount != null">#{withdrawableAmount},</if>
            <if test="serviceFee != null">#{serviceFee},</if>
            <if test="serviceFeeRate != null">#{serviceFeeRate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="timeQueryStr != null">#{timeQueryStr},</if>
            <if test="withdrawnAmountFee != null">#{withdrawnAmountFee},</if>
            <if test="notWithdrawnFee != null">#{notWithdrawnFee},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="leaderId != null">#{leaderId},</if>
            <if test="leaderNickName != null">#{leaderNickName},</if>
            <if test="parentLeaderId != null">#{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">#{parentLeaderNickName},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="totalWithdrawnFee != null">#{totalWithdrawnFee},</if>
            <if test="withdrawnNetCommission != null">#{withdrawnNetCommission},</if>
            <if test="notWithdrawnNetCommission != null">#{notWithdrawnNetCommission},</if>
            <if test="totalWithdrawnNetCommission != null">#{totalWithdrawnNetCommission},</if>
         </trim>
    </insert>

    <update id="updateEmployeeTeacherStatistics" parameterType="EmployeeTeacherStatistics">
        update `wendao101-order`.employee_teacher_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statisticsDate != null">statistics_date = #{statisticsDate},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="dealAmount != null">deal_amount = #{dealAmount},</if>
            <if test="withdrawnAmount != null">withdrawn_amount = #{withdrawnAmount},</if>
            <if test="moneyInTransit != null">money_in_transit = #{moneyInTransit},</if>
            <if test="withdrawableAmount != null">withdrawable_amount = #{withdrawableAmount},</if>
            <if test="serviceFee != null">service_fee = #{serviceFee},</if>
            <if test="serviceFeeRate != null">service_fee_rate = #{serviceFeeRate},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="withdrawnAmountFee != null">withdrawn_amount_fee = #{withdrawnAmountFee},</if>
            <if test="notWithdrawnFee != null">not_withdrawn_fee = #{notWithdrawnFee},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="leaderId != null">leader_id = #{leaderId},</if>
            <if test="leaderNickName != null">leader_nick_name = #{leaderNickName},</if>
            <if test="parentLeaderId != null">parent_leader_id = #{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name = #{parentLeaderNickName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="totalWithdrawnFee != null">total_withdrawn_fee = #{totalWithdrawnFee},</if>
            <if test="withdrawnNetCommission != null">withdrawn_net_commission = #{withdrawnNetCommission},</if>
            <if test="notWithdrawnNetCommission != null">not_withdrawn_net_commission = #{notWithdrawnNetCommission},</if>
            <if test="totalWithdrawnNetCommission != null">total_withdrawn_net_commission = #{totalWithdrawnNetCommission},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeTeacherStatisticsById" parameterType="Long">
        delete from `wendao101-order`.employee_teacher_statistics where id = #{id}
    </delete>

    <delete id="deleteEmployeeTeacherStatisticsByIds" parameterType="String">
        delete from `wendao101-order`.employee_teacher_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteEmployeeTeacherStatisticsBigerThanId">
        delete from `wendao101-order`.employee_teacher_statistics where id > #{id}
        <if test="timeRange != null  and timeRange != ''"> and time_query_str = #{timeRange}</if>
    </delete>
    <delete id="deleteEmployeeTeacherStatisticsBigerThanIdAndQueryStringIsNull">
        delete from `wendao101-order`.employee_teacher_statistics where id > #{id} and time_query_str is null
    </delete>
</mapper> 