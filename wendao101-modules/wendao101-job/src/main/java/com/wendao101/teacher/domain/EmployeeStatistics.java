package com.wendao101.teacher.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 教师统计对象 teacher_statistics
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeStatistics extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 老师交易金额
     */
    private BigDecimal dealAmount;
    /**
     * 老师已提现金额
     */
    private BigDecimal withdrawnAmount;
    /**
     * 老师在途资金
     */
    private BigDecimal moneyInTransit;
    /**
     * 老师可提现金额
     */
    private BigDecimal withdrawableAmount;
    /**
     * 时间查询区间2021-01-01 12:12:12~2024-01-01 12:12:12
     */
    private String timeQueryStr;
    /**
     * 已提现金额收取的毛抽佣费
     */
    private BigDecimal withdrawnAmountFee;
    /**
     * 未提现金额将来要抽的毛抽佣费用
     */
    private BigDecimal notWithdrawnFee;

    /**
     * 已提现金额收取的毛抽佣费+未提现金额将来要抽的毛抽佣费用
     */
    private BigDecimal totalWithdrawnFee;
    /**
     * 员工id
     */
    private Long employeeId;
    /**
     * 员工昵称姓名
     */
    private String nickName;
    
    /**
     * 直接主管id
     */
    private Long leaderId;
    
    /**
     * 直接主管昵称姓名
     */
    private String leaderNickName;
    
    /**
     * 上级主管id
     */
    private Long parentLeaderId;
    
    /**
     * 上级主管昵称姓名
     */
    private String parentLeaderNickName;
    
    /**
     * 订单数量
     */
    private Integer orderNum;

    /**
     * 已提现净抽佣
     */
    private BigDecimal withdrawnNetCommission;
    /**
     * 未提现净抽佣
     */
    private BigDecimal notWithdrawnNetCommission;

    /**
     * 已提现净抽佣+未提现净抽佣
     */
    private BigDecimal totalWithdrawnNetCommission;
} 